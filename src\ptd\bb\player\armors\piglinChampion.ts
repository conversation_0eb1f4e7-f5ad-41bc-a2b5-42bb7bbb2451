import { Player, system, Vector3 } from "@minecraft/server";
import { shockwave } from "../../bosses/general_attacks/shockwave";
import { cameraShake } from "../../bosses/general_effects/camerashake";

/**
 * Interface for tracking player crouch state
 */
interface PlayerCrouchState {
  /** Whether the player is currently crouching */
  isCrouching: boolean;
  /** Timestamp when crouch started */
  crouchStartTime: number;
  /** Whether the player is in the air (for slam detection) */
  isInAir: boolean;
  /** Whether slam attack is on cooldown */
  slamCooldown: boolean;
  /** Cooldown end time */
  cooldownEndTime: number;
}

/**
 * Map to track crouch states for each player
 */
const playerCrouchStates = new Map<string, PlayerCrouchState>();

/**
 * Constants for piglin champion armor mechanics
 */
const SLAM_CONSTANTS = {
  /** Minimum crouch time in ticks before slam can be triggered */
  MIN_CROUCH_TIME: 10,
  /** Slam damage radius */
  DAMAGE_RADIUS: 8,
  /** Slam damage amount */
  DAMAGE: 12,
  /** Slam knockback power */
  KNOCKBACK_POWER: 2.0,
  /** Cooldown time in ticks */
  COOLDOWN_TIME: 100, // 5 seconds
  /** Minimum height to trigger slam */
  MIN_SLAM_HEIGHT: 2.0
} as const;

/**
 * Handles piglin champion armor set mechanics for a player
 * @param player The player wearing the piglin champion armor set
 */
export function handlePiglinChampionArmor(player: Player): void {
  const playerId: string = player.id;

  // Initialize player state if not exists
  if (!playerCrouchStates.has(playerId)) {
    playerCrouchStates.set(playerId, {
      isCrouching: false,
      crouchStartTime: 0,
      isInAir: false,
      slamCooldown: false,
      cooldownEndTime: 0
    });
  }

  const state: PlayerCrouchState = playerCrouchStates.get(playerId)!;
  const currentTick: number = system.currentTick;

  // Check cooldown
  if (state.slamCooldown && currentTick >= state.cooldownEndTime) {
    state.slamCooldown = false;
  }

  // Skip if on cooldown
  if (state.slamCooldown) {
    return;
  }

  const isCrouchingNow: boolean = player.isSneaking;
  const isOnGround: boolean = player.isOnGround;

  // Handle crouch state changes
  if (isCrouchingNow && !state.isCrouching) {
    // Started crouching
    state.isCrouching = true;
    state.crouchStartTime = currentTick;
  } else if (!isCrouchingNow && state.isCrouching) {
    // Stopped crouching - check if we should trigger slam
    const crouchDuration: number = currentTick - state.crouchStartTime;

    if (crouchDuration >= SLAM_CONSTANTS.MIN_CROUCH_TIME && !isOnGround) {
      // Player released crouch after minimum time and is in air - prepare for slam
      state.isInAir = true;
    }

    state.isCrouching = false;
  }

  // Check for slam impact (player was in air and now on ground)
  if (state.isInAir && isOnGround) {
    executeSlamAttack(player);
    state.isInAir = false;
    state.slamCooldown = true;
    state.cooldownEndTime = currentTick + SLAM_CONSTANTS.COOLDOWN_TIME;
  }
}

/**
 * Executes the slam attack when player impacts the ground
 * @param player The player performing the slam
 */
function executeSlamAttack(player: Player): void {
  const playerLocation: Vector3 = player.location;

  // Play body slam particle effect (same as piglin champion)
  player.dimension.spawnParticle("ptd_bb:pg_foot_stomp1_01", playerLocation);

  // Play sound effects
  player.dimension.playSound("random.explode", playerLocation, { volume: 1.5, pitch: 0.8 });

  // Apply shockwave effect
  shockwave(
    player,
    SLAM_CONSTANTS.DAMAGE_RADIUS,
    SLAM_CONSTANTS.KNOCKBACK_POWER,
    SLAM_CONSTANTS.DAMAGE,
    ["piglin_champion", "piglin"] // Exclude piglin families
  );

  // Apply camera shake effect
  cameraShake(player, 32, 0.02, 0.5, 0.5);

  // Play additional explosion particle
  player.dimension.spawnParticle("minecraft:large_explosion", playerLocation);
}

/**
 * Cleanup function to remove player state when they disconnect
 * @param playerId The ID of the player to clean up
 */
export function cleanupPlayerState(playerId: string): void {
  playerCrouchStates.delete(playerId);
}